<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
      <tr>
        <th class="text-start">{{ column_name }}</th>
        <th class="text-start">{{ column_code }}</th>
        <th class="text-end">{{ column_orders }}</td>
        <th class="text-end">{{ column_total }}</th>
        <th class="text-end">{{ column_action }}</th>
      </tr>
    </thead>
    <tbody>
      {% if coupons %}
        {% for coupon in coupons %}
          <tr>
            <td class="text-start">{{ coupon.name }}</td>
            <td class="text-start">{{ coupon.code }}</td>
            <td class="text-end">{{ coupon.orders }}</td>
            <td class="text-end">{{ coupon.total }}</td>
            <td class="text-end"><a href="{{ coupon.edit }}" data-bs-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa-solid fa-pencil"></i></a></td>
          </tr>
        {% endfor %}
      {% else %}
        <tr>
          <td class="text-center" colspan="6">{{ text_no_results }}</td>
        </tr>
      {% endif %}
    </tbody>
  </table>
</div>
<div class="row">
  <div class="col-sm-6 text-start">{{ pagination }}</div>
  <div class="col-sm-6 text-end">{{ results }}</div>
</div>