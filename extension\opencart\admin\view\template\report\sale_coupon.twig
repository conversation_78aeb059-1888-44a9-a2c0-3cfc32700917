<div class="row">
  <div id="filter-report" class="col-lg-3 col-md-12 order-lg-last d-none d-lg-block mb-3">
    <div class="card">
      <div class="card-header"><i class="fa-solid fa-filter"></i> {{ text_filter }}</div>
      <div class="card-body">
        <form id="form-filter">
          <div class="mb-3">
            <label for="input-date-start" class="form-label">{{ entry_date_start }}</label>
            <input type="date" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ entry_date_start }}" id="input-date-start" class="form-control"/>
          </div>
          <div class="mb-3">
            <label for="input-date-end" class="form-label">{{ entry_date_end }}</label>
            <input type="date" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ entry_date_end }}" id="input-date-end" class="form-control"/>
          </div>
          <div class="text-end">
            <button type="button" id="button-filter" class="btn btn-light"><i class="fa-solid fa-filter"></i> {{ button_filter }}</button>
            <button type="reset" data-bs-toggle="tooltip" title="{{ button_reset }}" class="btn btn-outline-secondary"><i class="fa-solid fa-filter-circle-xmark"></i></button>
          </div>
        </form>
      </div>
    </div>
  </div>
  <div class="col col-lg-9 col-md-12">
    <div class="card">
      <div class="card-header"><i class="fa-solid fa-chart-bar"></i> {{ heading_title }}</div>
      <div id="sale-coupon" class="card-body">{{ list }}</div>
    </div>
  </div>
</div>
<script type="text/javascript"><!--
$('#sale-coupon').on('click', '.pagination a', function(e) {
    e.preventDefault();

    $('#sale-coupon').load(this.href);
});

$('#button-filter').on('click', function() {
    var url = '';

    var filter_date_start = $('#input-date-start').val();

    if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
    }

    var filter_date_end = $('#input-date-end').val();

    if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
    }

    $('#sale-coupon').load('index.php?route=extension/opencart/report/sale_coupon.list&user_token={{ user_token }}' + url);
});
//--></script>
