<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
      <tr>
        <th class="text-start">{{ column_comment }}</th>
        <th class="text-start">{{ column_ip }}</th>
        <th class="text-start">{{ column_date_added }}</th>
      </tr>
    </thead>
    <tbody>
      {% if activities %}
        {% for activity in activities %}
          <tr>
            <td class="text-start">{{ activity.comment }}</td>
            <td class="text-start">{{ activity.ip }}</td>
            <td class="text-start">{{ activity.date_added }}</td>
          </tr>
        {% endfor %}
      {% else %}
        <tr>
          <td class="text-center" colspan="3">{{ text_no_results }}</td>
        </tr>
      {% endif %}
    </tbody>
  </table>
</div>
<div class="row">
  <div class="col-sm-6 text-start">{{ pagination }}</div>
  <div class="col-sm-6 text-end">{{ results }}</div>
</div>