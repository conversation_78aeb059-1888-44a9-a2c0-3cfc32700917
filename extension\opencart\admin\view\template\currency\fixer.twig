{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="form-currency" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa-solid fa-save"></i></button>
        <a href="{{ back }}" data-bs-toggle="tooltip" title="{{ button_back }}" class="btn btn-light"><i class="fa-solid fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    <div class="card">
      <div class="card-header"><i class="fa-solid fa-pencil"></i> {{ text_edit }}</div>
      <div class="card-body">
        <div class="alert alert-info"><i class="fa-solid fa-circle-exclamation"></i> {{ text_signup }}</div>
        <form id="form-currency" action="{{ save }}" method="post" data-oc-toggle="ajax">
          <div class="row mb-3 required">
            <label for="entry-api" class="col-sm-2 col-form-label">{{ entry_api }}</label>
            <div class="col-sm-10">
              <input type="text" name="currency_fixer_api" value="{{ currency_fixer_api }}" placeholder="{{ entry_api }}" id="input-api" class="form-control"/>
              <div id="error-api" class="invalid-feedback"></div>
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label">{{ entry_status }}</label>
            <div class="col-sm-10">
              <div class="form-check form-switch form-switch-lg">
                <input type="hidden" name="currency_fixer_status" value="0"/>
                <input type="checkbox" name="currency_fixer_status" value="1" id="input-status" class="form-check-input"{% if currency_fixer_status %} checked{% endif %}/>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}