<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
      <tr>
        <th class="text-start">{{ column_name }}</th>
        <th class="text-start">{{ column_model }}</th>
        <th class="text-end">{{ column_quantity }}</td>
        <th class="text-end">{{ column_total }}</th>
      </tr>
    </thead>
    <tbody>
      {% if products %}
        {% for product in products %}
          <tr>
            <td class="text-start">{{ product.name }}</td>
            <td class="text-start">{{ product.model }}</td>
            <td class="text-end">{{ product.quantity }}</td>
            <td class="text-end">{{ product.total }}</td>
          </tr>
        {% endfor %}
      {% else %}
        <tr>
          <td class="text-center" colspan="4">{{ text_no_results }}</td>
        </tr>
      {% endif %}
    </tbody>
  </table>
</div>
<div class="row">
  <div class="col-sm-6 text-start">{{ pagination }}</div>
  <div class="col-sm-6 text-end">{{ results }}</div>
</div>