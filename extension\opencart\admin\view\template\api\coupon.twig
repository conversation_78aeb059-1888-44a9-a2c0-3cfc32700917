<div class="input-group">
  <section id="section-coupon" class="form-control" style="min-height: 64px;">
    <div class="lead">
      <strong>{{ text_coupon }}</strong>
      <br/>
      <div id="output-coupon">{{ coupon }}</div>
    </div>
  </section>
  <button type="button" data-bs-toggle="modal" data-bs-target="#modal-coupon" class="btn btn-outline-primary"><i class="fa-solid fa-cog"></i></button>
</div>
<div id="modal-coupon" class="modal">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{{ text_coupon }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="form-coupon" data-oc-target="section-coupon">
          <div class="mb-3">
            <label for="input-coupon" class="form-label">{{ entry_coupon }}</label>
            <input type="text" name="coupon" value="{{ coupon }}" placeholder="{{ entry_coupon }}" id="input-coupon" class="form-control"/>
          </div>
          <div class="text-end">
            <button type="submit" id="button-coupon" class="btn btn-primary"><i class="fa-solid fa-check"></i> {{ button_continue }}</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript"><!--
$('#form-coupon').on('submit', function(e) {
    e.preventDefault();

    $.ajax({
        url: 'index.php?route=sale/order.call&user_token={{ user_token }}&call=extension&code=coupon&store_id=' + $('#input-store').val() + '&language=' + $('#input-language').val() + '&currency=' + $('#input-currency').val(),
        type: 'post',
        data: $('#form-customer, #form-cart, #form-shipping-address, #form-shipping-method, #form-payment-address, #form-payment-method, #collapse-order form').serialize(),
        dataType: 'json',
        beforeSend: function() {
            $('#button-coupon').button('loading');
        },
        complete: function() {
            $('#button-coupon').button('reset');
        },
        success: function(json) {
            console.log(json);

            $('.alert-dismissible').remove();
            $('.is-invalid').removeClass('is-invalid');
            $('.invalid-feedback').removeClass('d-block');

            if (json['error']) {
                $('#form-coupon').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa-solid fa-circle-exclamation"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');

                $('#input-coupon').addClass('is-invalid');
            }

            if (json['success']) {
                $('#form-coupon').prepend('<div class="alert alert-success alert-dismissible"><i class="fa-solid fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');

                $('#output-coupon').html($('#input-coupon').val());

                cart_render(json['products'], json['totals'], json['shipping_required']);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});
//--></script>