{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="form-module" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa-solid fa-save"></i></button>
        <a href="{{ back }}" data-bs-toggle="tooltip" title="{{ button_back }}" class="btn btn-light"><i class="fa-solid fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    <div class="card">
      <div class="card-header"><i class="fa-solid fa-pencil"></i> {{ text_edit }}</div>
      <div class="card-body">
        <form id="form-module" action="{{ save }}" method="post" data-oc-toggle="ajax">
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label">{{ entry_admin }}</label>
            <div class="col-sm-10">
              <div class="btn-group">
                <input type="radio" name="module_store_admin" value="1" id="input-store-admin-yes" class="btn-check"{% if module_store_admin %} checked{% endif %}/> <label for="input-store-admin-yes" class="btn btn-outline-secondary">{{ text_yes }}</label>
                <input type="radio" name="module_store_admin" value="0" id="input-store-admin-no" class="btn-check"{% if not module_store_admin %} checked{% endif %}/> <label for="input-store-admin-no" class="btn btn-outline-secondary">{{ text_no }}</label>
              </div>
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label">{{ entry_status }}</label>
            <div class="col-sm-10">
              <div class="form-check form-switch form-switch-lg">
                <input type="hidden" name="module_store_status" value="0"/>
                <input type="checkbox" name="module_store_status" value="1" id="input-status" class="form-check-input"{% if module_store_status %} checked{% endif %}/>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}