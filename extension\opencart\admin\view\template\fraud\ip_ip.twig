<div class="table-responsive">
  <table class="table table-bordered table-hover">
    <thead>
      <tr>
        <td class="text-start">{{ column_ip }}</td>
        <td class="text-end">{{ column_total }}</td>
        <td class="text-start">{{ column_date_added }}</td>
        <td class="text-end">{{ column_action }}</td>
      </tr>
    </thead>
    <tbody>
      {% if ips %}
        {% for ip in ips %}
          <tr>
            <td class="text-start"><a href="http://www.geoiptool.com/en/?IP={{ ip.ip }}" target="_blank">{{ ip.ip }}</a></td>
            <td class="text-end"><a href="{{ ip.filter_ip }}" target="_blank">{{ ip.total }}</a></td>
            <td class="text-start">{{ ip.date_added }}</td>
            <td class="text-end"><button type="button" value="{{ ip.ip }}" data-bs-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa-solid fa-circle-minus"></i></button></td>
          </tr>
        {% endfor %}
      {% else %}
        <tr>
          <td class="text-center" colspan="4">{{ text_no_results }}</td>
        </tr>
      {% endif %}
    </tbody>
  </table>
</div>
<div class="row">
  <div class="col-sm-6 text-start">{{ pagination }}</div>
  <div class="col-sm-6 text-end">{{ results }}</div>
</div>
