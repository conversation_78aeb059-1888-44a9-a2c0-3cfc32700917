<div class="card mb-3">
  <div class="card-header"><i class="fa-solid fa-shopping-cart"></i> {{ heading_title }}</div>
  <div class="table-responsive">
    <table class="table">
      <thead>
        <tr>
          <td class="text-end">{{ column_order_id }}</td>
          <td>{{ column_customer }}</td>
          <td>{{ column_status }}</td>
          <td>{{ column_date_added }}</td>
          <td class="text-end">{{ column_total }}</td>
          <td class="text-end">{{ column_action }}</td>
        </tr>
      </thead>
      <tbody>
        {% if orders %}
          {% for order in orders %}
            <tr>
              <td class="text-end">{{ order.order_id }}</td>
              <td>{{ order.customer }}</td>
              <td>{{ order.status }}</td>
              <td>{{ order.date_added }}</td>
              <td class="text-end">{{ order.total }}</td>
              <td class="text-end"><a href="{{ order.view }}" data-bs-toggle="tooltip" title="{{ button_view }}" class="btn btn-info"><i class="fa-solid fa-eye"></i></a></td>
            </tr>
          {% endfor %}
        {% else %}
          <tr>
            <td class="text-center" colspan="6">{{ text_no_results }}</td>
          </tr>
        {% endif %}
      </tbody>
    </table>
  </div>
</div>
