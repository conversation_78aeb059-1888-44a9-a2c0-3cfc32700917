<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
      <tr>
        <td class="text-start">{{ column_customer }}</td>
        <td class="text-start">{{ column_email }}</td>
        <td class="text-start">{{ column_customer_group }}</td>
        <td class="text-start">{{ column_status }}</td>
        <td class="text-end">{{ column_total }}</td>
        <td class="text-end">{{ column_action }}</td>
      </tr>
    </thead>
    <tbody>
      {% if subscriptions %}
        {% for subscription in subscriptions %}
          <tr>
            <td class="text-start">{{ subscription.customer }}</td>
            <td class="text-start">{{ subscription.email }}</td>
            <td class="text-start">{{ subscription.customer_group }}</td>
            <td class="text-start">{{ subscription.status }}</td>
            <td class="text-end">{{ subscription.total }}</td>
            <td class="text-end"><a href="{{ subscription.edit }}" data-bs-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa-solid fa-pencil"></i></a></td>
          </tr>
        {% endfor %}
      {% else %}
        <tr>
          <td class="text-center" colspan="6">{{ text_no_results }}</td>
        </tr>
      {% endif %}
    </tbody>
  </table>
</div>
<div class="row">
  <div class="col-sm-6 text-start">{{ pagination }}</div>
  <div class="col-sm-6 text-end">{{ results }}</div>
</div>