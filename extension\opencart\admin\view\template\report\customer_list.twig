<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
      <tr>
        <th class="text-start">{{ column_date_start }}</th>
        <th class="text-start">{{ column_date_end }}</th>
        <th class="text-end">{{ column_total }}</th>
      </tr>
    </thead>
    <tbody>
      {% if customers %}
        {% for customer in customers %}
          <tr>
            <td class="text-start">{{ customer.date_start }}</td>
            <td class="text-start">{{ customer.date_end }}</td>
            <td class="text-end">{{ customer.total }}</td>
          </tr>
        {% endfor %}
      {% else %}
        <tr>
          <td class="text-center" colspan="3">{{ text_no_results }}</td>
        </tr>
      {% endif %}
    </tbody>
  </table>
</div>
<div class="row">
  <div class="col-sm-6 text-start">{{ pagination }}</div>
  <div class="col-sm-6 text-end">{{ results }}</div>
</div>