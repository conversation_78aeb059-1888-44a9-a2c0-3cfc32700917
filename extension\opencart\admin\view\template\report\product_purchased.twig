<div class="row">
  <div id="filter-report" class="col-lg-3 col-md-12 order-lg-last d-none d-lg-block mb-3">
    <div class="card">
      <div class="card-header"><i class="fa-solid fa-filter"></i> {{ text_filter }}</div>
      <div class="card-body">
      <form id="form-filter">
        <div class="mb-3">
          <label for="input-order-status" class="form-label">{{ entry_order_status }}</label>
          <select name="filter_order_status_id" id="input-order-status" class="form-select">
            <option value="0">{{ text_all_status }}</option>
            {% for order_status in order_statuses %}
              <option value="{{ order_status.order_status_id }}"{% if order_status.order_status_id == filter_order_status_id %} selected{% endif %}>{{ order_status.name }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="mb-3">
          <label for="input-date-start" class="form-label">{{ entry_date_start }}</label>
          <input type="date" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ entry_date_start }}" id="input-date-start" class="form-control"/>
        </div>
        <div class="mb-3">
          <label for="input-date-end" class="form-label">{{ entry_date_end }}</label>
          <input type="date" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ entry_date_end }}" id="input-date-end" class="form-control"/>
        </div>
        <div class="text-end">
          <button type="button" id="button-filter" class="btn btn-light"><i class="fa-solid fa-filter"></i> {{ button_filter }}</button>
          <button type="reset" data-bs-toggle="tooltip" title="{{ button_reset }}" class="btn btn-outline-secondary"><i class="fa-solid fa-filter-circle-xmark"></i></button>
        </div>
      </form>
      </div>
    </div>
  </div>
  <div class="col col-lg-9 col-md-12">
    <div class="card">
      <div class="card-header"><i class="fa-solid fa-chart-bar"></i> {{ heading_title }}</div>
      <div id="product-purchased" class="card-body">{{ list }}</div>
    </div>
  </div>
</div>
<script type="text/javascript"><!--
$('#product-purchased').on('click', '.pagination a', function(e) {
    e.preventDefault();

    $('#product-purchased').load(this.href);
});

$('#button-filter').on('click', function() {
    var url = '';

    var filter_order_status_id = $('#input-order-status').val();

    if (filter_order_status_id != 0) {
        url += '&filter_order_status_id=' + encodeURIComponent(filter_order_status_id);
    }

    var filter_date_start = $('#input-date-start').val();

    if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
    }

    var filter_date_end = $('#input-date-end').val();

    if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
    }

    $('#product-purchased').load('index.php?route=extension/opencart/report/product_purchased.list&user_token={{ user_token }}' + url);
});
//--></script>
